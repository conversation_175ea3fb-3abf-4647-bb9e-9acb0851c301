# Test Game G1 - <PERSON><PERSON> cảnh khác nhau

## Cài đặt dữ liệu test

### 1. Tạo prizes trong database

```sql
-- Prizes cho OOH/Frame context
INSERT INTO prizes (name, description, winrate, quantity, type, campaign_id, game_id, biz_storage_id, active, display_order) VALUES
('Voucher 200K với HĐ từ 599K', 'voucher_200k', 50, 100, 'voucher', 1, 27, 12345, 1, 1),
('Mặt nạ sủi bọt miễn phí', 'mat_na_sui_bot', 30, 50, 'voucher', 1, 27, 12346, 1, 2),
('Code nước giặt 169K', 'nuoc_giat_169k', 25, 30, 'voucher', 1, 27, 12347, 1, 3),
('Tặng 1 đôi tất', 'tat_nam_nu', 20, 200, 'voucher', 1, 27, 12348, 1, 4),
('Thẻ ưu tiên <PERSON>h to<PERSON>', 'uu_tien_thanh_toan', 15, 100, 'voucher', 1, 27, 12349, 1, 5),
('Merchandise miễn phí', 'merchandise', 10, 20, 'voucher', 1, 27, 12350, 1, 6),
('Mã quay ô tô', 'ma_quay_o_to', 5, 5, 'voucher', 1, 27, 12351, 1, 7),

-- Prizes cho Store Fashion context
('Code giảm 30% thời trang', 'giam_30_thoi_trang', 100, 150, 'voucher', 1, 27, 12352, 1, 8),

-- Prizes cho Store Accessories context  
('Code giảm 30% giày thể thao', 'giam_30_giay_the_thao', 100, 80, 'voucher', 1, 27, 12353, 1, 9),
('Code giảm 20% balo', 'giam_20_balo', 30, 60, 'voucher', 1, 27, 12354, 1, 10),

-- Prizes cho Store Cosmetics context
('Code giảm 10% mỹ phẩm', 'giam_10_my_pham', 100, 120, 'voucher', 1, 27, 12355, 1, 11),
('Combo 99K 3 chai nước rửa bát', 'combo_99k', 25, 40, 'voucher', 1, 27, 12356, 1, 12),

-- Prizes cho Store Payment context
('Gói quà miễn phí kiểu Nhật', 'goi_qua_mien_phi', 50, 30, 'voucher', 1, 27, 12357, 1, 13),

-- Lose prizes
('Chúc bạn may mắn lần sau!', 'lose_message', 0, 9999, 'lose', 1, 27, NULL, 1, 14);
```

### 2. Tạo game record

```sql
INSERT INTO games (name, campaign_id, slug, active, start_date, end_date) VALUES
('Ưu đãi quanh ta - Hiện ra quà lớn', 1, 'g1', 1, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY));
```

## Test Cases

### Test 1: OOH/Frame Context - Lần đầu chơi

**Request:**
```bash
curl -X POST "http://localhost:3000/api/game/g1/claim" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "context": "ooh_frame"
  }'
```

**Expected Result:**
- `is_first_time_in_context: true`
- Bắt buộc trúng 1 trong các voucher miễn phí (voucher_200k, mat_na_sui_bot, etc.)
- `play_turns` giảm đi 1

### Test 2: OOH/Frame Context - Lần thứ 2

**Request:** (sau khi đã chơi lần 1 với context "ooh_frame")
```bash
curl -X POST "http://localhost:3000/api/game/g1/claim" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "context": "ooh_frame"
  }'
```

**Expected Result:**
- `is_first_time_in_context: false`
- Tỷ lệ trúng giảm xuống ~30% so với lần đầu
- Có khả năng trúng "lose" message

### Test 3: Store Fashion Context - Lần đầu

**Request:**
```bash
curl -X POST "http://localhost:3000/api/game/g1/claim" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "context": "store_fashion"
  }'
```

**Expected Result:**
- `is_first_time_in_context: true`
- Bắt buộc trúng "Code giảm 30% thời trang" (winrate = 100)

### Test 4: Store Fashion Context - Lần thứ 2

**Request:** (sau khi đã chơi lần 1 với context "store_fashion")
```bash
curl -X POST "http://localhost:3000/api/game/g1/claim" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "context": "store_fashion"
  }'
```

**Expected Result:**
- `is_first_time_in_context: false`
- Có 30% cơ hội trúng "Voucher 200K"
- Có 70% cơ hội trúng "lose" message

### Test 5: Store Accessories Context - Lần đầu

**Request:**
```bash
curl -X POST "http://localhost:3000/api/game/g1/claim" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "context": "store_accessories"
  }'
```

**Expected Result:**
- `is_first_time_in_context: true`
- Bắt buộc trúng "Code giảm 30% giày thể thao" (winrate = 100)

### Test 6: Store Cosmetics Context - Lần đầu

**Request:**
```bash
curl -X POST "http://localhost:3000/api/game/g1/claim" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "context": "store_cosmetics"
  }'
```

**Expected Result:**
- `is_first_time_in_context: true`
- Bắt buộc trúng "Code giảm 10% mỹ phẩm" (winrate = 100)

### Test 7: Store Payment Context - Lần đầu

**Request:**
```bash
curl -X POST "http://localhost:3000/api/game/g1/claim" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "context": "store_payment"
  }'
```

**Expected Result:**
- `is_first_time_in_context: true`  
- Bắt buộc trúng "Thẻ ưu tiên thanh toán" (winrate = 100)

### Test 8: Context không hợp lệ

**Request:**
```bash
curl -X POST "http://localhost:3000/api/game/g1/claim" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "context": "invalid_context"
  }'
```

**Expected Result:**
- Trả về context mặc định "ooh_frame" hoặc xử lý như context không hợp lệ

### Test 9: Không truyền context

**Request:**
```bash
curl -X POST "http://localhost:3000/api/game/g1/claim" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{}'
```

**Expected Result:**
- Sử dụng context mặc định "ooh_frame"

### Test 10: Hết lượt chơi

**Request:** (sau khi đã hết play_turns)
```bash
curl -X POST "http://localhost:3000/api/game/g1/claim" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "context": "ooh_frame"
  }'
```

**Expected Result:**
```json
{
  "status": "no_turns",
  "message": "Bạn đã hết lượt chơi hôm nay",
  "received_rewards": [...],
  "share_remaining": 1
}
```

## Cách kiểm tra database

### 1. Kiểm tra lịch sử chơi

```sql
SELECT 
  sh.userId,
  sh.spinTime,
  sh.description,
  p.name as prize_name,
  p.description as prize_desc
FROM spin_histories sh
LEFT JOIN prizes p ON sh.prizeId = p.id
WHERE sh.userId = {user_id}
ORDER BY sh.spinTime DESC;
```

### 2. Kiểm tra UserSpin

```sql
SELECT * FROM user_spins 
WHERE userId = {user_id} AND gameId = 27;
```

### 3. Kiểm tra số lượng prizes còn lại

```sql
SELECT 
  p.name,
  p.description,
  p.quantity,
  (SELECT COUNT(*) FROM spin_histories sh WHERE sh.prizeId = p.id) as used_count,
  (p.quantity - (SELECT COUNT(*) FROM spin_histories sh WHERE sh.prizeId = p.id)) as remaining
FROM prizes p
WHERE p.game_id = 27;
```

## Expected Behavior Summary

1. **Mỗi ngữ cảnh được track riêng biệt** - lần đầu chơi trong mỗi context sẽ có tỷ lệ trúng cao
2. **OOH/Frame:** Lần đầu 100% trúng voucher, lần sau giảm xuống ~30%  
3. **Store areas:** Lần đầu 100% trúng voucher chính của khu vực, lần sau theo tỷ lệ được định
4. **Response luôn include:** `context`, `is_first_time_in_context`, `play_turns`
5. **Prizes được filter** theo context và remaining quantity
6. **Winrate được adjust** động theo context và lần chơi 