# Test Logic cho Game G2 - Box Opening

## Tóm tắt thay đổi

Đã sửa lại logic `claimVoucher` cho game G2 theo yêu cầu:

### 1. Logic mới:
- Sử dụng `luckyWheelService.randomPrize(prizes)` để random prize giống G1
- Nếu `prize.type === 'voucher'` và `MAX_WINNING_BOXES > 0` thì random xem box nào trúng trong danh sách gửi lên
- Các box còn lại trong danh sách gửi lên sẽ là `type === 'lose'`
- Các box không được gửi lên sẽ được lấp đầy bằng các phần quà còn lại (cả trúng và không trúng) random và không trùng với các box được gửi lên

### 2. Cấu hình MAX_WINNING_BOXES:
- `MAX_WINNING_BOXES = 0`: Random prize có thể trúng hoặc không (tu<PERSON> theo randomPrize)
- `MAX_WINNING_BOXES = 1`: Bắt buộc có 1 box gửi lên trúng (không cần random)
- `MAX_WINNING_BOXES = 2`: Bắt buộc có 2 box gửi lên trúng (không cần random)

### 3. Test Cases cần kiểm tra:

#### Test Case 1: MAX_WINNING_BOXES = 0
```json
Request: {"box_ids": [1,6,9]}
Expected:
- Nếu randomPrize.type === 'voucher': 1 box trong [1,6,9] trúng voucher, 2 box còn lại không trúng
- Nếu randomPrize.type === 'lose': Tất cả 3 box gửi lên đều không trúng
```

#### Test Case 2: MAX_WINNING_BOXES = 1
```json
Request: {"box_ids": [1,6,9]}
Expected: Bắt buộc 1 box trong [1,6,9] trúng voucher, 2 box còn lại không trúng (không phụ thuộc randomPrize)
```

#### Test Case 3: MAX_WINNING_BOXES = 2
```json
Request: {"box_ids": [1,6,9]}
Expected: Bắt buộc 2 box trong [1,6,9] trúng voucher, 1 box còn lại không trúng (không phụ thuộc randomPrize)
```

#### Test Case 4: MAX_WINNING_BOXES = 3 (nhiều hơn số box gửi lên)
```json
Request: {"box_ids": [1,6,9]}
Expected: Tất cả 3 box gửi lên đều trúng voucher (không phụ thuộc randomPrize)
```

#### Test Case 5: MAX_WINNING_BOXES = 0 với nhiều lần test
```json
Request: {"box_ids": [1,6,9]}
Expected: Kết quả khác nhau tùy theo randomPrize - đôi khi trúng, đôi khi không
```

### 4. Kiểm tra logic:

1. **Random Prize**: Sử dụng `luckyWheelService.randomPrize(prizes)` giống G1
2. **Winning Logic**: Chỉ trúng khi `randomizedPrize.type === 'voucher'` và `MAX_WINNING_BOXES > 0`
3. **Box Assignment**: 
   - Winning boxes: Sử dụng voucher prizes (type !== 'lose')
   - Losing boxes trong danh sách gửi lên: Sử dụng lose prizes
   - Remaining boxes: Random từ tất cả prizes không trùng với đã sử dụng
4. **Coupon Processing**: Xử lý coupon cho từng winning box riêng biệt
5. **SpinHistory**: Lưu history cho mỗi winning box

### 5. Cải tiến so với logic cũ:

1. **Tuân theo random prize**: Logic giờ phụ thuộc vào kết quả random prize
2. **Flexible winning count**: Có thể config số box trúng qua MAX_WINNING_BOXES
3. **Better prize distribution**: Tránh trùng lặp prizes giữa các box
4. **Proper error handling**: Xử lý lỗi coupon tốt hơn
5. **Cleaner code**: Code dễ đọc và maintain hơn

### 6. Lưu ý khi test:

- Cần có prizes với type = 'voucher' và type = 'lose' trong database
- Cần có bizStorageId cho voucher prizes để test coupon
- Test với các giá trị MAX_WINNING_BOXES khác nhau
- Kiểm tra response format đúng theo API documentation
