# Tokyo Life Game G1 API Documentation

## Game G1: "ƯU ĐÃI QUANH TA - HIỆN RA QUÀ LỚN"

### Cách chơi
Người chơi sẽ quét QR tại các Frame tại thang máy, OOH hoặc standee dựng trước các quầy hàng. <PERSON>hi người chơi quét xong, giao diện AR hiển thị các quà tặng ảo bay lượn quanh không gian cửa hàng, trên các kệ sản phẩm, các gian hàng khác nhau. Người chơi di chuyển đến các vị trí có xuất hiện quà tặng trên màn hình để bắt quà bằng cách nhấn nút "Bắt".

### Có 2 ngữ cảnh chơi:

#### Ngữ cảnh 1: QR tại OOH/Frame (`context: "ooh_frame"`)
**<PERSON><PERSON><PERSON> đích:** Tặng quà miễn phí để lôi kéo KH tới cửa hàng

**<PERSON><PERSON> sách quà:**
1. Voucher 200K với hóa đơn nguyên giá 599K 
2. Tặng miễn phí mặt nạ sủi bọt dưỡng da (Gny tới 45k) với hoá đơn 599k bất kể kể cả giảm giá
3. Code mua rẻ nước giặt 169k với hoá đơn từ 499k bất kể
4. Tặng 1 đôi tất nam/ nữ với hóa đơn bất kể từ 399K
5. Thẻ "Ưu tiên thanh toán" (được mời thanh toán riêng không phải xếp hàng)
6. Code quà tặng Miễn phí: Tặng merchandise (túi, ô, bình nước,...) /huy hiệu campaign. Giới hạn 20 quà miễn phí trong vòng 1 tháng 
7. Mã quay trúng thưởng ô tô

#### Ngữ cảnh 2: QR tại các khu vực trong cửa hàng
**Mục đích:** Tặng code giảm giá mua sản phẩm theo từng khu vực

##### A. Khu vực Thời trang nam, nữ, trẻ em (`context: "store_fashion"`)
- **Lần đầu (100%):** Code giảm 30% mua thời trang nguyên giá bất kể
- **Lần sau (30%):** Voucher 200K với hóa đơn nguyên giá 599K

##### B. Khu vực Phụ kiện Balo, giày, túi ví (`context: "store_accessories"`)
- **Lần đầu (100%):** Code Giảm 30% toàn bộ giày thể thao (Gny từ 599k)
- **Lần sau (30%):** Code Giảm 20% Balo thông minh, Tặng 1 đôi tất nam/ nữ với hóa đơn bất kể từ 399K

##### C. Khu Vực Hóa mỹ phẩm (`context: "store_cosmetics"`)
- **Lần đầu (100%):** Code Giảm 10% mỹ phẩm TokyoLife
- **Lần sau (25% mỗi loại):** 
  - Tặng miễn phí mặt nạ sủi bọt dưỡng da với hoá đơn 599k bất kể
  - Code mua rẻ nước giặt 169k với hoá đơn từ 499k bất kể
  - Combo 99K 3 chai nước rửa bát 1 lít

##### D. Khu vực thanh toán (`context: "store_payment"`)
- **Lần đầu (100%):** Thẻ "Ưu tiên thanh toán"
- **Lần sau (50%):** 1 thẻ Gói quà Miễn phí kiểu Nhật

## API Endpoints

### 1. Khởi tạo game
```
GET /api/game/g1/init
Authorization: Bearer {token}
```

**Response:**
```json
{
  "status": "success",
  "user": {
    "id": 123,
    "name": "Nguyễn Văn A"
  },
  "play_turns": 2
}
```

### 2. Bắt đầu chơi
```
GET /api/game/g1/play
Authorization: Bearer {token}
```

**Response:**
```json
{
  "status": "success",
  "play_turns": 2,
  "vouchers": [
    {
      "id": 1,
      "value": "Voucher 200K",
      "image": "voucher_200k.png"
    },
    {
      "id": 2,
      "value": "Mặt nạ sủi bọt",
      "image": "mat_na.png"
    }
  ]
}
```

### 3. Claim voucher (Bắt quà)
```
POST /api/game/g1/claim
Authorization: Bearer {token}
Content-Type: application/json

{
  "context": "ooh_frame" // hoặc "store_fashion", "store_accessories", "store_cosmetics", "store_payment"
}
```

**Context Values:**
- `"ooh_frame"` - QR tại OOH/Frame
- `"store_fashion"` - Khu vực thời trang  
- `"store_accessories"` - Khu vực phụ kiện
- `"store_cosmetics"` - Khu vực hóa mỹ phẩm
- `"store_payment"` - Khu vực thanh toán

**Response thành công:**
```json
{
  "status": "success",
  "message": "Bạn nhận được Voucher 200K!",
  "context": "ooh_frame",
  "is_first_time_in_context": true,
  "play_turns": 1
}
```

**Response hết lượt:**
```json
{
  "status": "no_turns",
  "message": "Bạn đã hết lượt chơi hôm nay",
  "received_rewards": [
    {
      "id": 1,
      "name": "Voucher 200K"
    }
  ],
  "share_remaining": 1
}
```

### 4. Chia sẻ để nhận thêm lượt
```
POST /api/game/g1/share
Authorization: Bearer {token}
Content-Type: application/json

{
  "platform": "facebook"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Bạn đã nhận thêm 1 lượt chơi!",
  "play_turns": 2
}
```

## Logic xử lý

### Tỷ lệ trúng theo ngữ cảnh

#### OOH/Frame
- **Lần đầu:** Bắt buộc trúng 1 trong các voucher miễn phí
- **Lần sau:** Tỷ lệ trúng giảm xuống 30% giá trị winrate gốc

#### Store Areas  
- **Lần đầu trong mỗi khu vực:** 100% trúng voucher chính của khu vực đó
- **Lần sau:** Tỷ lệ theo mô tả (30%, 25%, 50%)

### Cách setup prizes trong database

Để hệ thống hoạt động đúng, cần setup field `description` trong bảng `prizes` với các keyword:

**OOH/Frame prizes:**
- `voucher_200k` 
- `mat_na_sui_bot`
- `nuoc_giat_169k`
- `tat_nam_nu` 
- `uu_tien_thanh_toan`
- `merchandise`
- `ma_quay_o_to`

**Store Fashion:**
- `giam_30_thoi_trang`
- `voucher_200k`

**Store Accessories:**
- `giam_30_giay_the_thao`
- `giam_20_balo`
- `tat_nam_nu`

**Store Cosmetics:**
- `giam_10_my_pham`
- `mat_na_sui_bot`
- `nuoc_giat_169k`
- `combo_99k`

**Store Payment:**
- `uu_tien_thanh_toan`
- `goi_qua_mien_phi`

### Ví dụ dữ liệu prizes

```sql
INSERT INTO prizes (name, description, winrate, quantity, type, campaign_id, game_id, biz_storage_id) VALUES
('Voucher 200K với HĐ từ 599K', 'voucher_200k', 50, 100, 'voucher', 1, 27, 12345),
('Mặt nạ sủi bọt miễn phí', 'mat_na_sui_bot', 30, 50, 'voucher', 1, 27, 12346),
('Code giảm 30% thời trang', 'giam_30_thoi_trang', 100, 200, 'voucher', 1, 27, 12347);
```
