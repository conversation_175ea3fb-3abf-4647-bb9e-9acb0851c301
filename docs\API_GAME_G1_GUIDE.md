# 🎮 Tokyo Life Game G1 - API Usage Guide


### 1. 🚀 **Initialize Game**

Khởi tạo game, preload user data và game UI. User sẽ nhận thêm 1 lượt chơi mỗi ngày mới.

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/init' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ'
```

**Response Success:**
```json
{
    "status": "success",
    "user": {
        "id": "25442",
        "name": "<PERSON><PERSON><PERSON>n <PERSON>"
    },
    "play_turns": 1
}
```

### 2. 🎲 **Start Game**

Bắt đầu game và lấy danh sách vouchers sẽ xuất hiện trong game.


**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/play' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ'
```

**Response Success:**
```json
{
    "status": "success",
    "play_turns": 2,
    "vouchers": [
        {
            "id": 10431,
            "value": "KAM - Kho Voucher",
            "image": "https://kam-gift.mediacdn.vn/kam-voucher-10.png"
        },
        {
            "id": 10432,
            "value": "KAM - Voucher 20%",
            "image": "https://kam-gift.mediacdn.vn/kam-voucher-20.png"
        },
        {
            "id": 10433,
            "value": "KAM - Voucher 30%",
            "image": "https://kam-gift.mediacdn.vn/kam-voucher-30.png"
        }
    ]
}
```

### 3. 🎁 **Claim Voucher**

**Request Body:**
```json
{
  "voucher_id": 2
}
```

**Example Request:**
```bash
curl --location --request POST 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/claim' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ'
```

**Response Success (Còn lượt chơi):**
```json
{
    "status": "success",
    "message": "Bạn nhận được Voucher giảm giá 10%",
    "play_turns": 0
}
```

**Response Success (Hết voucher):**
```json
{
  "status": "success",
  "message": "Chúc bạn may mắn lần sau",
  "play_turns": 2
}
```

**Response (Hết lượt chơi):**
```json
{
  "status": "no_turns",
  "message": "Bạn đã hết lượt chơi hôm nay",
  "received_rewards": [
    {
      "id": 1,
      "name": "Voucher 50K"
    },
    {
      "id": 2, 
      "name": "Voucher 100K"
    }
  ],
  "share_remaining": 1
}
```

### 4. 📤 **Share for Extra Turn**


**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/share' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ' \
--header 'Content-Type: application/json' \
--data '{
    "platform": "facebook"
}'
```

**Response Success:**
```json
{
  "status": "success",
  "message": "Bạn đã nhận thêm 1 lượt chơi!",
  "play_turns": 3
}
```

**Response (Đã share hôm nay):**
```json
{
  "status": "already_shared",
  "message": "Bạn đã chia sẻ và nhận lượt hôm nay rồi"
}
```

**Response (Platform không hỗ trợ):**
```json
{
  "status": "error",
  "message": "Không hỗ trợ platform này"
}
```

## 🚨 Error Handling

### Common Error Responses:

**Missing Parameters:**
```json
{
  "status": "error",
  "message": "Missing game slug"
}
```

**Authentication Error:**
```json
{
  "status": "error",
  "message": "Unauthorized"
}
```

---

# 🎯 Tokyo Life Game G2 - API Usage Guide

Game G2 là phiên bản nâng cao với cơ chế mở hộp quà. Người chơi có thể chọn nhiều hộp cùng lúc để mở và nhận voucher.

### 1. 🚀 **Initialize Game G2**

Khởi tạo game G2, preload user data và game UI. User sẽ nhận thêm lượt chơi mỗi ngày mới.

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g2/init' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ'
```

**Response Success:**
```json
{
    "status": "success",
    "user": {
        "id": 123,
        "name": "Nguyen Van A"
    },
    "play_turns": 3
}
```

### 2. 🎁 **Claim Boxes (Mở Hộp Quà)**

Chọn và mở nhiều hộp quà cùng lúc. Người chơi có thể chọn từ 1-9 hộp để mở.

**Request Body:**
```json
{
  "box_ids": [1, 6, 9]
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g2/claim' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ' \
--header 'Content-Type: application/json' \
--data '{
  "box_ids": [7,2,3]
}'
```

**Response Success (Còn lượt chơi):**
```json
{
    "status": "success",
    "message": "Bạn đã mở hộp quà số 3 và nhận được voucher!",
    "boxes": [
        {
            "id": 1,
            "reward": {
                "name": "KAM - Voucher 20%",
                "image": "https://kam-gift.mediacdn.vn/kam-voucher-20.png"
            }
        },
        {
            "id": 2,
            "reward": {
                "name": "Chúc bạn may mắn lần sau",
                "image": "https://kam-gift.mediacdn.vn/pt5.png"
            }
        },
        {
            "id": 3,
            "reward": {
                "name": "KAM - Voucher 10%",
                "image": "https://kam-gift.mediacdn.vn/kam-voucher-10.png"
            }
        },
        {
            "id": 4,
            "reward": {
                "name": "Chúc bạn may mắn lần sau",
                "image": "https://kam-gift.mediacdn.vn/pt5.png"
            }
        },
        {
            "id": 5,
            "reward": {
                "name": "KAM - Voucher 30%",
                "image": "https://kam-gift.mediacdn.vn/kam-voucher-30.png"
            }
        },
        {
            "id": 6,
            "reward": {
                "name": "Chúc bạn may mắn lần sau",
                "image": "https://kam-gift.mediacdn.vn/pt5.png"
            }
        },
        {
            "id": 7,
            "reward": {
                "name": "Chúc bạn may mắn lần sau",
                "image": "https://kam-gift.mediacdn.vn/pt5.png"
            }
        },
        {
            "id": 8,
            "reward": {
                "name": "Chúc bạn may mắn lần sau",
                "image": "https://kam-gift.mediacdn.vn/pt5.png"
            }
        },
        {
            "id": 9,
            "reward": {
                "name": "Chúc bạn may mắn lần sau",
                "image": "https://kam-gift.mediacdn.vn/pt5.png"
            }
        }
    ],
    "play_turns": 78
}
```

**Response (Hết lượt chơi):**
```json
{
    "status": "no_turns",
    "message": "Bạn đã hết lượt chơi hôm nay",
    "boxes": [
        {"id": 1, "reward": {"name": "Voucher 20K", "image": "/assets/v20k.png"}},
        {"id": 2, "reward": {"name": "Voucher 50K", "image": "/assets/v50k.png"}},
        {"id": 3, "reward": {"name": "Voucher 100K", "image": "/assets/v100k.png"}},
        {"id": 4, "reward": {"name": "Voucher 150K", "image": "/assets/v150k.png"}},
        {"id": 5, "reward": {"name": "Voucher 200K", "image": "/assets/v200k.png"}},
        {"id": 6, "reward": {"name": "Voucher 250K", "image": "/assets/v250k.png"}},
        {"id": 7, "reward": {"name": "Voucher 300K", "image": "/assets/v300k.png"}},
        {"id": 8, "reward": {"name": "Voucher 350K", "image": "/assets/v350k.png"}},
        {"id": 9, "reward": {"name": "Voucher 400K", "image": "/assets/v400k.png"}}
    ],
    "share_remaining": 1
}
```

### 3. 📤 **Share for Extra Turn G2**

Chia sẻ để nhận thêm 1 lượt chơi (giới hạn 1 lần mỗi ngày).

**Request Body:**
```json
{
  "platform": "facebook"
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g2/share' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ' \
--header 'Content-Type: application/json' \
--data '{
    "platform": "facebook"
}'
```

**Response Success:**
```json
{
    "status": "success",
    "message": "Bạn đã nhận thêm 1 lượt chơi!",
    "play_turns": 1
}
```

**Response (Đã share hôm nay):**
```json
{
    "status": "already_shared",
    "message": "Bạn đã chia sẻ và nhận lượt hôm nay rồi"
}
```

*Last updated: 2025-07-17* 