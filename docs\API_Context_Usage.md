# API Game G1 & G2 - Context Usage Guide

## Tổng quan

API Game đã được cập nhật để hỗ trợ các ngữ cảnh khác nhau theo yêu cầu từ file `game 1 - game 2.md`. Mỗi game có các ngữ cảnh riêng với logic phần quà khác nhau.

## Game G1 - ƯU ĐÃI QUANH TA - HIỆN RA QUÀ LỚN

### Endpoint: POST /api/game/g1/claim

### Request Body:
```json
{
  "box_ids": [1, 2, 3],
  "context": "ooh_frame" | "store_area",
  "area": "fashion" | "accessories" | "cosmetics" | "checkout" // chỉ cần khi context = "store_area"
}
```

### Ngữ cảnh 1: OOH/Frame
**Context**: `"ooh_frame"`

**Danh sách quà (ID 1-7):**
1. Voucher 200K với hóa đơn nguyên giá 599K
2. Tặng miễn phí mặt nạ sủi bọt dưỡng da với hoá đơn 599k
3. <PERSON> mua rẻ nước giặt 169k với hoá đơn từ 499k
4. Tặng 1 đôi tất nam/nữ với hóa đơn từ 399K
5. Thẻ "Ưu tiên thanh toán"
6. Code quà tặng Miễn phí merchandise
7. Mã quay trúng thưởng ô tô

**Logic:**
- **Lần 1**: 100% trúng Voucher 200K (ID 1)
- **Lần 2**: Tỷ lệ theo phần trăm:
  - ID 2: 15%
  - ID 3: 15% 
  - ID 4: 15%
  - ID 5: 15%
  - ID 6: 10%
  - ID 7: 10%
  - Chúc may mắn: 20%

### Ngữ cảnh 2: Khu vực cửa hàng
**Context**: `"store_area"`
**Area**: Cần chỉ định khu vực cụ thể

#### Khu vực Thời trang (`"fashion"`)
- **Lần 1**: 100% Code giảm 30% thời trang nguyên giá
- **Lần 2**: 30% Voucher 200K, 70% chúc may mắn

#### Khu vực Phụ kiện (`"accessories"`)
- **Lần 1**: 100% Code Giảm 30% toàn bộ giày thể thao
- **Lần 2**: 30% Code Giảm 20% Balo thông minh hoặc Tặng 1 đôi tất, 70% chúc may mắn

#### Khu vực Hóa mỹ phẩm (`"cosmetics"`)
- **Lần 1**: 100% Code Giảm 10% mỹ phẩm TokyoLife
- **Lần 2**: 25% cho mỗi loại (mặt nạ, nước giặt, combo 99K), 25% chúc may mắn

#### Khu vực thanh toán (`"checkout"`)
- **Lần 1**: 100% Thẻ "Ưu tiên thanh toán"
- **Lần 2**: 50% Gói quà Miễn phí kiểu Nhật, 50% chúc may mắn

## Game G2 - DEAL HỜI HÔM NAY - LẤY NGAY KẺO TIẾC

### Endpoint: POST /api/game/g2/claim

### Request Body:
```json
{
  "box_ids": [1, 2, 3],
  "context": "roadshow" | "social_media"
}
```

### Ngữ cảnh 1: Activation RoadShow
**Context**: `"roadshow"`

**Danh sách quà (ID 1-9):**
1. Mã quay trúng thưởng ô tô
2. Code giảm 200K cho áo chống nắng từ 490K
3. Code tặng sản phẩm "Tự hào Việt Nam"
4. Voucher 100k với hóa đơn từ 699k
5. Tặng miễn phí 1 áo Polo với hóa đơn từ 799K
6. Giảm 30% kính gấp gọn mới
7. Tặng miễn phí 1 chai nước rửa bát với hóa đơn từ 399K
8. Code mua rẻ combo 66K
9. Thẻ "Ưu tiên thanh toán"

### Ngữ cảnh 2: Link truyền thông
**Context**: `"social_media"`

**Danh sách quà (ID 1-9):**
1. Mã quay trúng thưởng ô tô
2. Code giảm 200K cho áo chống nắng từ 490K
3. Code tặng sản phẩm "Tự hào Việt Nam"
4. Voucher 100k với hóa đơn từ 699k
5. Giảm 30% toàn bộ balo thông minh
6. Tặng miễn phí 1 áo Polo với hóa đơn từ 799K
7. Tặng miễn phí 1 nước lau sàn với hóa đơn từ 399K
8. Code giảm 30% thời trang nguyên giá
9. Code đổi vật phẩm đặc biệt

**Logic cho cả 2 ngữ cảnh:**
- Mỗi user có 1 lượt chọn 3 hộp quà
- Tỷ lệ: 1 hộp "chúc may mắn", 2 hộp còn lại chia đều tỷ lệ các quà còn lại

## Response Format

### Thành công:
```json
{
  "status": "success",
  "message": "Bạn nhận được [tên quà]",
  "play_turns": 1,
  "boxes": [...] // chỉ có trong G2
}
```

### Hết lượt:
```json
{
  "status": "no_turns", 
  "message": "Bạn đã hết lượt chơi hôm nay",
  "received_rewards": [...], // chỉ có trong G1
  "share_remaining": 1
}
```

## Lưu ý Implementation

1. **Context validation**: API sẽ fallback về logic cũ nếu không có context
2. **Prize mapping**: Cần đảm bảo ID prizes trong database khớp với logic
3. **Quantity check**: Tất cả prizes đều được kiểm tra số lượng còn lại
4. **Logging**: Tất cả request đều được log với context và area information
