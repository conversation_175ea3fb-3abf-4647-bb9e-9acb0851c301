import { AppDataSource } from '@/config/config'
import { LuckyPrize } from '@/entities/LuckyPrize'
import { UserSpin } from '@/entities/UserSpin'
import { SpinHistory } from '@/entities/SpinHistory'
import { WaterUserGift } from '@/entities/WaterUserGift'
import { SpinHistoryCode, TblGames, VoucherDraw, TblUsers } from '@/entities'
import { API_ENDPOINTS } from '@/config/constant'
import { LogActivity } from '@/entities/LogActivity'
import axios from 'axios'
import { CommonService } from './common.service'
import FormData from 'form-data';
import { Like, Not } from 'typeorm'

// Interface cho response từ bizfly coupon API
interface CouponResponse {
  error: number
  status: boolean
  message: string
  data: CouponData
}

interface CouponData {
  coupon_id: number;
  storage_id: number;
  client_id: number;
  uid: number;
  code: string;
  name: string;
  current_use: number;
  max_use: number;
  coupon_status: number;
  received_id_moment: string;
  transaction_id: string | null;
  total_coupon_released: number;
  martket_voucher_id: string | null;
  storage_title: string;
  storage_desc: string;
  project_id: number;
  vid: number;
  storage_type: string;
  storage_discount: number;
  min_discount: number;
  max_discount: number;
  extra: string;
  type_duration: string;
  duration: number;
  start_date: string;
  end_date: string;
  exp_type: string | null;
  exp_date: string | null;
  total_day_use: number | null;
  total_coupon: number;
  total_use: number;
  apply_time: string | null;
  status: number;
  storage_created_date: string;
  storage_updated_date: string;
  deleted_at: string | null;
  config_create_code: string;
  max_use_coupon: number;
  mess_gift_skip: string;
  carrier_id: number | null;
  product_id: number | null;
  storage_source: string | null;
  storage_avatar: string;
  storage_branch: string;
  link_scan_qr_code: string;
}

interface SendCouponResponse {
  status: number
  msg: string
  data: {
    transaction_id: string
  }
  code: unknown[],
  log_id: string
}

const prizeRepo = () => AppDataSource.getRepository(LuckyPrize)
const userSpinRepo = () => AppDataSource.getRepository(UserSpin)
const spinHistoryRepo = () => AppDataSource.getRepository(SpinHistory)
const spinHistoryCodeRepo = () => AppDataSource.getRepository(SpinHistoryCode)
const waterUserGiftRepo = () => AppDataSource.getRepository(WaterUserGift)
const gameRepo = () => AppDataSource.getRepository(TblGames)
const logActivityRepo = () => AppDataSource.getRepository(LogActivity)
const voucherDrawRepo = () => AppDataSource.getRepository(VoucherDraw)
const userRepo = () => AppDataSource.getRepository(TblUsers)

function getNowGMT7(): Date {
  const now = new Date();
  return new Date(now.getTime() + 7 * 60 * 60 * 1000);
}

function sub7Hours(times: Date): Date {
  const now = new Date(times);
  return new Date(now.getTime() - 7 * 60 * 60 * 1000);
}

// Helper function để tránh duplicate code
async function logActivity(userId: number, name: string, result: any, config: any, formDataObj: any) {
  const log = new LogActivity();
  log.user_id = userId;
  log.name = name;
  
  if (result && result.status) {
    log.message = `Send coupon to bizfly status: ${result.status}, message: ${result.msg || result.message}`;
  } else {
    log.message = `Send coupon to bizfly failed: ${result?.message || 'Unknown error'}`;
  }
  
  log.full_message = JSON.stringify(result);
  log.url = config.url;
  log.method = 'POST';
  log.form_data = JSON.stringify(formDataObj);
  log.created_at = getNowGMT7();
  log.updated_at = getNowGMT7();
  
  await logActivityRepo().save(log);
}


// Function để gọi API lấy coupon từ bizfly
// async function sendCouponToBizfly(
//   customerId: string,
//   storageId: string,
//   storageName: string,
//   couponCode: string,
//   status: string,
//   source: string,
//   userId: number
// ): Promise<any> {
//   try {
//     console.log('sendCouponToBizfly', customerId, storageId, storageName, couponCode, status, source);
//     const data = new FormData();
//     data.append('customer_id', customerId);
//     data.append('storage_id', storageId);
//     data.append('storage_name', storageName);
//     data.append('coupon_code', couponCode);
//     data.append('status', status);
//     data.append('source', source);

//     const config = {
//       method: 'post',
//       maxBodyLength: Infinity,
//       url: 'https://dev.tokyolife.kamgift.vn/cms/api/v1/coupon/update',
//       headers: {
//         'Authorization': 'bc4337d2214996821bf977b9e6f5bf4d',
//         'Cookie': 'laravel_session=WNTD6UZSCqD9v222ZhEjGvA19pgtJ9NfpnuUla99',
//         ...data.getHeaders()
//       },
//       data: data
//     };

//     const response = await axios.request(config);
//     const result = response.data;
//     console.log('sendCouponToBizfly result', result);

//     // Log the response
//     const log = new LogActivity();
//     log.user_id = userId;
//     log.name = 'sendCouponToBizfly';
//     log.message = `Send coupon to bizfly status: ${result.status}, message: ${result.msg || result.message}`;
//     log.full_message = JSON.stringify(result);
//     log.url = config.url;
//     log.method = 'POST';
    
//     // Lưu lại dữ liệu thực tế trong FormData
//     const formDataObj = {
//       customer_id: customerId,
//       storage_id: storageId,
//       storage_name: storageName,
//       coupon_code: couponCode,
//       status: status,
//       source: source
//     };
//     log.form_data = JSON.stringify(formDataObj);
    
//     log.created_at = getNowGMT7();
//     log.updated_at = getNowGMT7();
//     await logActivityRepo().save(log);

//     return result;
//   } catch (error) {
//     console.error('Error calling bizfly API:', error);
//     return null;
//   }
// }

async function sendCouponToBizfly(
  customerId: string, 
  storageId: number, 
  storageName: string, 
  couponCode: string, 
  status: string, 
  source: string, 
  userId: number
): Promise<any> {
  const data = JSON.stringify({
    "table": "data_coupon",
    "data": [
      {
        "fields": [
          {
            "key": "customer",
            "value": [
              {
                "id": customerId
              }
            ]
          },
          {
            "key": "coupon_code",
            "value": couponCode
          },
          {
            "key": "storage_id",
            "value": storageId
          },
          {
            "key": "storage_name",
            "value": storageName
          },
          {
            "key": "trang_thai",
            "value": [
              {
                "value": status
              }
            ]
          },
          {
            "key": "source",
            "value": [
              {
                "value": source
              }
            ]
          }
        ]
      }
    ]
  });
  const timeNow = CommonService.getCurrentTime();
  const config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: 'https://crm.bizfly.vn/_api/base-table/update',
    headers: { 
      'cb-access-key': 'R6UvzzwN6ede0b0ac06e1aca34589f4f1bb73be3RA9x2TnW', 
      'cb-project-token': '96e21d1c-e275-4b98-b564-5595f49f688d', 
      'cb-access-sign': CommonService.createHmacWithCurrentTime('96e21d1c-e275-4b98-b564-5595f49f688d', 'dc6521ac1aab36ddf902c3b4ead17cdbcf37bab8', timeNow), 
      'cb-access-timestamp': timeNow, 
      'Content-Type': 'application/json',
      'Cookie': 'giftbizfly_session=Tbb368x1Rdruss7b2AqFIcGQLVCYJ6XNIgdL5UmJ'
    },
    data: data
  };
  
  try {
    const response = await axios.request(config);
    const result = response.data;
    console.log('sendCouponToBizfly result', result);

    // Log the successful response
    await logActivity(userId, 'sendCouponToBizfly', result, config, {
      customer_id: customerId,
      storage_id: storageId,
      storage_name: storageName,
      coupon_code: couponCode,
      status: status,
      source: source
    });

    return result;
  } catch (error) {
    console.error('Error calling bizfly API:', error);
    
    // Log the error response
    await logActivity(userId, 'sendCouponToBizfly', error, config, {
      customer_id: customerId,
      storage_id: storageId,
      storage_name: storageName,
      coupon_code: couponCode,
      status: status,
      source: source
    });

    return null;
  }
}

// Function để lưu voucher draw vào database
async function saveVoucherDraw(
  userId: number,
  campaignId: number,
  gameId: number,
  phone: string,
  voucherCode: string,
  storage_id: string
): Promise<void> {
  try {
    const voucherDraw = new VoucherDraw()
    voucherDraw.userId = userId
    voucherDraw.campaignId = campaignId
    voucherDraw.gameId = gameId
    voucherDraw.phone = phone
    voucherDraw.voucherCode = voucherCode
    voucherDraw.storageId = storage_id
    voucherDraw.createdAt = getNowGMT7()
    
    await voucherDrawRepo().save(voucherDraw)
    console.log('Đã lưu voucher draw thành công:', voucherCode)
  } catch (error) {
    console.error('Lỗi khi lưu voucher draw:', error)
  }
}

// Function để gọi API lấy coupon từ bizfly
export async function getCouponFromBizfly( userId: number, storageId: number, tokyoId: string, bizId: string, gameName: string, campaignId: number, gameId: number, type: number): Promise<CouponResponse | null> {
  try {
   const headers = {
      "cb-2fa-token": "15ffd678b3e9bd332df28eed109b2bfedafcbafc2bcb74312081fea6f00715a6",
      "Content-Type": "application/json",
      "Cookie": "giftbizfly_session=Tbb368x1Rdruss7b2AqFIcGQLVCYJ6XNIgdL5UmJ"
    };

    const data = {
      "project_token": "96e21d1c-e275-4b98-b564-5595f49f688d",
      "client_id": 515,
      "storage_id": storageId,
      "user_received_id": tokyoId.toString(),
      "check_user_received": 1
    };

    const response = await axios.post(API_ENDPOINTS.getCoupon, data, { headers });
    const result = response.data;
    console.log('result', result);

    // Lưu thông tin voucher vào bảng voucher_draw nếu thành công
    if (result.error === 0 && result.status === true && result.data) {
      const responseSendCoupon = await sendCouponToBizfly(bizId, result.data.storage_id, result.data.storage_title, result.data.code, 'Chưa sử dụng', gameName, userId);
      console.log('responseSendCoupon', responseSendCoupon);
      // Lấy thông tin user để có số điện thoại
      const user = await userRepo().findOne({ where: { id: userId } });
      const userPhone = user?.phone || '';
      if(type == 3){
        await saveVoucherDraw(
          userId,
          campaignId,
          gameId,
          userPhone,
          result.data.code,
          result.data.storage_id
        );
      }
    }
  
    // Log the response
    const log = new LogActivity();
    log.user_id = userId;
    log.name = 'getCouponFromBizfly';
    log.message = `Get coupon from bizfly status: ${result.status}, message: ${result.message}`;
    log.full_message = JSON.stringify(result);
    log.url = API_ENDPOINTS.getCoupon;
    log.method = 'POST';
    log.form_data = JSON.stringify(data);
    log.created_at = getNowGMT7();
    log.updated_at = getNowGMT7();
    await logActivityRepo().save(log);
    
    if (result.error === 0 && result.status === true) {
      return result;
    }
    
    console.error('Error getting coupon from bizfly:', result);
    return null;
  } catch (error) {
    console.error('Error calling bizfly API:', error);
    return null;
  }
}

export async function getInfoCustomerFromBizfly(userId: number, phone: string): Promise<any> {
  const requestData = {
    "table": "data_customer",
    "limit": 1,
    "skip": 0,
    "query": { "phones.value": phone }
  };
  const timeNow = CommonService.getCurrentTime();
  const projectToken = '96e21d1c-e275-4b98-b564-5595f49f688d';
  const accessKey = 'R6UvzzwN6ede0b0ac06e1aca34589f4f1bb73be3RA9x2TnW';
  const secretKey = 'dc6521ac1aab36ddf902c3b4ead17cdbcf37bab8';

  const url = API_ENDPOINTS.getTableBase;
  const config = {
    headers: {
      'cb-access-key': accessKey,
      'cb-project-token': projectToken,
      'cb-access-sign': CommonService.createHmacWithCurrentTime(projectToken, secretKey, timeNow),
      'cb-access-timestamp': timeNow,
      'Content-Type': 'application/json',
      'Cookie': 'giftbizfly_session=Tbb368x1Rdruss7b2AqFIcGQLVCYJ6XNIgdL5UmJ'
    }
  };

  try {
    const response = await axios.post(url, requestData, config);
    const result = response.data;
    console.log('getInfoCustomerFromBizfly result', result);

    // Log the response
    const log = new LogActivity();
    log.user_id = userId;
    log.name = 'getInfoCustomerFromBizfly';
    log.message = `Get info customer from bizfly status: ${result.status}, message: ${result.message || result.msg}`;
    log.full_message = JSON.stringify(result);
    log.url = url;
    log.method = 'POST';
    log.form_data = JSON.stringify(requestData);
    log.created_at = getNowGMT7();
    log.updated_at = getNowGMT7();
    await logActivityRepo().save(log);

    return result;
  } catch (error) {
    console.error('Error calling bizfly API:', error);
    
    // Log the error
    const log = new LogActivity();
    log.user_id = userId;
    log.name = 'getInfoCustomerFromBizfly - ERROR';
    log.message = `Get info customer from bizfly failed: ${error.message}`;
    log.full_message = JSON.stringify(error);
    log.url = url;
    log.method = 'POST';
    log.form_data = JSON.stringify(requestData);
    log.created_at = getNowGMT7();
    log.updated_at = getNowGMT7();
    await logActivityRepo().save(log);

    return null;
  }
}

async function createUserGift(userId: number,rewardCode: string, campaignId: number, gameId: number){
    const waterUserGift = new WaterUserGift()
    waterUserGift.userId = userId
    waterUserGift.rewardCode = rewardCode
    waterUserGift.times = 0
    waterUserGift.currWaterGiftConfigId = 0
    waterUserGift.active = 0
    waterUserGift.createdAt = getNowGMT7()
    waterUserGift.updatedAt = getNowGMT7()
    waterUserGift.campaignId = campaignId
    waterUserGift.gameId = gameId
    await waterUserGiftRepo().save(waterUserGift)
}

export const getActivePrizes = async (campaignId: number, gameId: number) => {
  return prizeRepo().find({ where: { campaignId, active: 1, gameId: gameId } })
}

export const getActivePrizesContext = async (campaignId: number, gameId: number, context: string) => {
  return prizeRepo().find({ where: { campaignId, active: 1, gameId: gameId, context: Like(`%"${context}"%`), type: Not('voucher100') } })
}

export const getActivePrizesVoucher100 = async (campaignId: number, gameId: number, context: string) => {
  return prizeRepo().find({ where: { campaignId, active: 1, gameId: gameId, type: 'voucher100', context: Like(`%"${context}"%`) } })
}

export const getUserSpin = async (userId: number, campaignId: number, gameId: number) => {
  return userSpinRepo().findOne({ where: { userId, campaignId, gameId } })
}

export const decreaseUserSpin = async (userId: number) => {
  const userSpin = await userSpinRepo().findOne({ where: { userId } })
  if (userSpin && userSpin.spinCounts > 0) {
    userSpin.spinCounts -= 1
    userSpin.updatedAt = getNowGMT7()
    await userSpinRepo().save(userSpin)
  }
}

export const saveSpinHistory = async (
  userId: number, 
  prizeId: number, 
  campaignId: number, 
  gameId: number,
  couponData?: { couponId: number; couponCode: string; couponName: string; qrCodeLink: string }
) => {
  const history = new SpinHistory()
  history.userId = userId
  history.prizeId = prizeId
  history.spinTime = getNowGMT7()
  history.createdAt = getNowGMT7()
  history.updatedAt = getNowGMT7()
  history.campaignId = campaignId
  history.gameId = gameId
  history.status = 0
  
  // Lưu thông tin coupon nếu có
  if (couponData) {
    history.bizCouponId = couponData.couponId
    history.voucherCode = couponData.couponCode
    history.voucherName = couponData.couponName
    history.voucherLink = couponData.qrCodeLink
  }
  await spinHistoryRepo().save(history)
}

export const saveSpinHistoryCode = async (
  userId: number, 
  prizeId: number, 
  campaignId: number, 
  gameId: number,
  couponData?: { couponId: number; couponCode: string; couponName: string; qrCodeLink: string }
) => {
  const history = new SpinHistoryCode()
  history.userId = userId
  history.prizeId = prizeId
  history.spinTime = getNowGMT7()
  history.createdAt = getNowGMT7()
  history.updatedAt = getNowGMT7()
  history.campaignId = campaignId
  history.gameId = gameId
  history.status = 0
  
  // Lưu thông tin coupon nếu có
  if (couponData) {
    history.bizCouponId = couponData.couponId
    history.voucherCode = couponData.couponCode
    history.voucherName = couponData.couponName
    history.voucherLink = couponData.qrCodeLink
  }
  await spinHistoryCodeRepo().save(history)
}

export const randomPrize = (prizes: LuckyPrize[]): LuckyPrize | null => {
  const total = prizes.reduce((sum, p) => sum + p.winrate, 0)
  let rand = Math.random() * total
  for (const prize of prizes) {
    if (rand < prize.winrate) return prize
    rand -= prize.winrate
  }
  return null
}

export const addSpinCount = async (userId: number, campaignId: number, gameId: number) => {
  console.log('addSpinCount', userId, campaignId, gameId)
  const userSpin = await userSpinRepo().findOne({ where: { userId, campaignId, gameId } })
  console.log('userSpin', userSpin)
  const now = new Date()
  const nowVn = getNowGMT7();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  if (!userSpin) {
    const newUserSpin = new UserSpin()
    newUserSpin.userId = userId
    newUserSpin.spinCounts = 1
    newUserSpin.createdAt = nowVn
    newUserSpin.updatedAt = nowVn
    newUserSpin.lastRequest = nowVn
    newUserSpin.points = 0
    newUserSpin.campaignId = campaignId
    newUserSpin.gameId = gameId
    await userSpinRepo().save(newUserSpin)
    return 1
  }
 
  if (!userSpin.lastRequest || new Date(sub7Hours(userSpin.lastRequest)).getTime() < today.getTime()) {
    userSpin.spinCounts += 1
    userSpin.updatedAt = nowVn
    userSpin.lastRequest = nowVn
    await userSpinRepo().save(userSpin)
  }
  return userSpin.spinCounts
}

function generateRandomString(length: number, chars: string) {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

function generateRewardCode(): string {
  const numbers = '0123456789'
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const randomNumbers = generateRandomString(3, numbers)
  const randomLetters = generateRandomString(2, letters)
  return `KAM${randomNumbers}${randomLetters}`
}

export const spinLuckyWheel = async (userId: number, campaignId: number, tokyoId: string, bizId: string, type: number, gameId: number) => {
  const game = await gameRepo().findOne({ where: { campaignId, active: 1, type: type } })
  
  if (!game) {
    throw new Error('Không tìm thấy game')
  }
  
  const now = getNowGMT7();
  if (now < game.start_date || now > game.end_date) {
    throw new Error(now < game.start_date ? 'Game chưa bắt đầu' : 'Sự kiện đã kết thúc! Hãy theo dõi sự kiện quay mã trên fanpage của aFamily để tìm ra người may mắn nhận được vé bạn nhé!')
  }

  const userSpin = await getUserSpin(userId, campaignId, gameId)
  if (!userSpin || userSpin.spinCounts <= 0) {
    throw new Error('Bạn đã hết lượt quay')
  }
  const prizes = await getActivePrizes(campaignId, game.id)
  let prize = randomPrize(prizes)
  if (!prize) throw new Error('Không có phần thưởng phù hợp')
  
  let couponData = null
  console.log('prize', {...prize})
  // Kiểm tra số lượng prize còn lại và gọi API nếu cần
  if (prize.type === 'voucher' && prize.bizStorageId) {
    // Đếm số lần prize này đã được trúng
    const wonCount = await spinHistoryRepo()
      .createQueryBuilder('spin_history')
      .where('spin_history.prizeId = :prizeId', { prizeId: prize.id })
      .getCount()
    console.log('wonCount', wonCount)
    const remainingQuantity = prize.quantity - wonCount
    console.log('remainingQuantity', remainingQuantity)
    // Nếu còn số lượng, gọi API lấy coupon
    if (remainingQuantity > 0) {
      const couponResponse = await getCouponFromBizfly(userId, prize.bizStorageId, tokyoId, bizId, game.name, campaignId, game.id, type)
      if (couponResponse && couponResponse.data) {
        couponData = {
          couponId: couponResponse.data.coupon_id,
          couponCode: couponResponse.data.code,
          couponName: couponResponse.data.name,
          qrCodeLink: couponResponse.data.link_scan_qr_code
        }
      } else {
        // Nếu API trả về false, tìm prize có type = 'lose' để thay thế
        const losePrize = prizes.find(p => p.type === 'lose')
        if (losePrize) {
          prize = losePrize
        }
      }
    } else {
      // Nếu hết số lượng, tìm prize có type = 'lose' để thay thế
      const losePrize = prizes.find(p => p.type === 'lose')
      if (losePrize) {
        prize = losePrize
      }
    }
  }

  if(type == 2){
   await saveSpinHistory(userId, prize.id, campaignId, game.id, couponData)
  }
  if(type == 3){
    await saveSpinHistoryCode(userId, prize.id, campaignId, game.id, couponData)
  }
  // Trừ lượt quay
  userSpin.spinCounts -= 1;

  // Cộng point nếu có
  // if (prize.point && prize.point > 0) {
  //   userSpin.points = (userSpin.points || 0) + prize.point
  //   if (userSpin.points >= 5) {
  //     let rewardCode = generateRewardCode()
  //     let exist = await waterUserGiftRepo().findOne({ where: { rewardCode, campaignId } })
  //     while (exist) {
  //       rewardCode = generateRewardCode()
  //       exist = await waterUserGiftRepo().findOne({ where: { rewardCode, campaignId } })
  //     }
  //     userSpin.points -= 5
  //     await createUserGift(userId, rewardCode, campaignId, game.id)
  //     await userSpinRepo().save(userSpin)
  //     return { prize, rewardCode, couponData }
  //   }
  // }

  await userSpinRepo().save(userSpin)
  // Tạo rewardCode nếu prize.type là voucher
  // if (prize.type === 'voucher') {
  //   let rewardCode = generateRewardCode()
  //   let exist = await waterUserGiftRepo().findOne({ where: { rewardCode, campaignId } })
  //   while (exist) {
  //     rewardCode = generateRewardCode()
  //     exist = await waterUserGiftRepo().findOne({ where: { rewardCode, campaignId } })
  //   }
    
  //   await createUserGift(userId, rewardCode, campaignId, game.id)
  //   return { prize, rewardCode, couponData }
  // }

  return { prize, couponData }
}

export const getSpinHistoryByUser = async (userId: number, campaignId: number, gameId: number, type: number) => {
  console.log('getSpinHistoryByUser', userId, campaignId, type);
  if(type == 2){
    return spinHistoryRepo()
    .createQueryBuilder('spin_histories')
    .leftJoinAndMapOne('spin_histories.prize', LuckyPrize, 'prize', 'prize.id = spin_histories.prizeId')
    .where('spin_histories.userId = :userId', { userId })
    .andWhere('spin_histories.campaignId = :campaignId', { campaignId })
    .andWhere('spin_histories.gameId = :gameId', { gameId })
    .orderBy('spin_histories.spinTime', 'DESC')
    .getMany()
  }
  if(type == 3){
    return spinHistoryCodeRepo()
    .createQueryBuilder('spin_history_codes')
    .leftJoinAndMapOne('spin_history_codes.prize', LuckyPrize, 'prize', 'prize.id = spin_history_codes.prizeId')
    .where('spin_history_codes.userId = :userId', { userId })
    .andWhere('spin_history_codes.campaignId = :campaignId', { campaignId })
    .andWhere('spin_history_codes.gameId = :gameId', { gameId })
    .orderBy('spin_history_codes.spinTime', 'DESC')
    .getMany()
  }
}

export const getDailyStats = async (campaignId: number, gameId: number) => {
  // Lấy tổng số lượt quay trong ngày từ bảng UserSpin
  const totalSpins = await userSpinRepo()
    .createQueryBuilder('user_spins')
    .where('user_spins.campaignId = :campaignId', { campaignId })
    .andWhere('user_spins.gameId = :gameId', { gameId })
    .getCount()

  // Lấy số lượng rewardCode theo điều kiện
  const totalRewardCodes = await waterUserGiftRepo()
    .createQueryBuilder('water_user_gifts')
    .where('water_user_gifts.active = :active', { active: 0 })
    .andWhere('water_user_gifts.times = :times', { times: 0 })
    .andWhere('water_user_gifts.currWaterGiftConfigId = :configId', { configId: 0 })
    .andWhere('water_user_gifts.campaignId = :campaignId', { campaignId })
    .getCount()

  return {
    totalSpins: totalSpins + 5000,
    totalRewardCodes: totalRewardCodes + 800
  }
}
