# Hướng dẫn Test Game G2 Logic

## Cách thay đổi cấu hình

Trong file `src/services/gameG1.service.ts`, dòng 253:

```typescript
const MAX_WINNING_BOXES = 0; // Thay đổi giá trị này để test
```

## Test Cases chi tiết

### 1. Test MAX_WINNING_BOXES = 0 (Random Logic)

**Cấu hình:**
```typescript
const MAX_WINNING_BOXES = 0;
```

**Request:**
```json
POST /api/game/g2/claim
{
  "box_ids": [1, 6, 9]
}
```

**Expected Behavior:**
- Sử dụng `luckyWheelService.randomPrize(prizes)` để quyết định
- Nếu `randomPrize.type === 'voucher'`: 1 box trong [1,6,9] sẽ trúng
- Nếu `randomPrize.type === 'lose'`: Tất cả box trong [1,6,9] đều không trúng
- Kết quả sẽ khác nhau mỗi lần test

### 2. Test MAX_WINNING_BOXES = 1 (Bắt buộc 1 box trúng)

**Cấu hình:**
```typescript
const MAX_WINNING_BOXES = 1;
```

**Request:**
```json
POST /api/game/g2/claim
{
  "box_ids": [1, 6, 9]
}
```

**Expected Behavior:**
- Bắt buộc có 1 box trong [1,6,9] trúng voucher
- 2 box còn lại không trúng (type = 'lose')
- Không phụ thuộc vào `randomPrize`
- Kết quả ổn định: luôn có 1 box trúng

### 3. Test MAX_WINNING_BOXES = 2 (Bắt buộc 2 box trúng)

**Cấu hình:**
```typescript
const MAX_WINNING_BOXES = 2;
```

**Request:**
```json
POST /api/game/g2/claim
{
  "box_ids": [1, 6, 9]
}
```

**Expected Behavior:**
- Bắt buộc có 2 box trong [1,6,9] trúng voucher
- 1 box còn lại không trúng (type = 'lose')
- Không phụ thuộc vào `randomPrize`
- Kết quả ổn định: luôn có 2 box trúng

### 4. Test MAX_WINNING_BOXES = 5 (Nhiều hơn số box gửi lên)

**Cấu hình:**
```typescript
const MAX_WINNING_BOXES = 5;
```

**Request:**
```json
POST /api/game/g2/claim
{
  "box_ids": [1, 6, 9]
}
```

**Expected Behavior:**
- Tất cả 3 box trong [1,6,9] đều trúng voucher
- Không có box nào không trúng trong danh sách gửi lên
- `actualWinningBoxCount = Math.min(5, 3) = 3`

## Kiểm tra Response Format

**Response khi có box trúng:**
```json
{
  "status": "success",
  "message": "Bạn đã mở hộp quà số 1, 6 và nhận được voucher!",
  "boxes": [
    {"id": 1, "reward": {"name": "Voucher 20K", "image": "/assets/v20k.png"}},
    {"id": 2, "reward": {"name": "Chúc bạn may mắn lần sau", "image": "/assets/lose.png"}},
    // ... 7 boxes khác
  ],
  "play_turns": 2
}
```

**Response khi không có box nào trúng:**
```json
{
  "status": "false",
  "message": "Chúc bạn may mắn lần sau!",
  "boxes": [
    // ... 9 boxes với rewards
  ],
  "play_turns": 2
}
```

## Kiểm tra Database

### SpinHistory
Sau mỗi lần claim, kiểm tra bảng `spin_histories`:
- Mỗi box trúng sẽ có 1 record
- `prizeId` phải match với prize của box trúng
- `couponId`, `couponCode` có giá trị nếu lấy coupon thành công

### UserSpin
- `spinCounts` giảm 1 sau mỗi lần claim
- `updatedAt` được cập nhật

## Debug Tips

1. **Log actualWinningBoxCount:**
```typescript
console.log('MAX_WINNING_BOXES:', MAX_WINNING_BOXES);
console.log('actualWinningBoxCount:', actualWinningBoxCount);
console.log('winningBoxes:', winningBoxes);
```

2. **Log randomPrize khi MAX_WINNING_BOXES = 0:**
```typescript
if (MAX_WINNING_BOXES === 0) {
  const randomizedPrize = luckyWheelService.randomPrize(prizes);
  console.log('randomizedPrize:', randomizedPrize);
}
```

3. **Kiểm tra prizes distribution:**
```typescript
console.log('winPrizes count:', winPrizes.length);
console.log('losePrizes count:', losePrizes.length);
```

## Lưu ý quan trọng

1. **Database cần có prizes với type = 'voucher' và type = 'lose'**
2. **Voucher prizes cần có bizStorageId để test coupon**
3. **Test với user có spinCounts > 0**
4. **Mỗi lần test nên reset database hoặc dùng user khác để tránh ảnh hưởng**
